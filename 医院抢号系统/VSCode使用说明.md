# VSCode 一键运行说明

## 🎯 问题解决

你遇到的问题：在VSCode中点击三角形运行main.py时，无法正确读取config.json中的token。

## ✅ 解决方案

我为你创建了专门的VSCode运行脚本，已经内置了最新的token，可以直接运行！

## 🚀 使用方法

### 方法1：使用 vscode_run.py（推荐）

1. **在VSCode中打开** `医院抢号系统/vscode_run.py`
2. **点击右上角的三角形运行按钮** ▶️
3. **按提示选择操作**：
   - 输入 `1` - 立即开始抢号
   - 输入 `2` - 设置定时抢号  
   - 输入 `3` - 仅测试功能

### 方法2：使用 run_booking.py（交互式）

1. **在VSCode中打开** `医院抢号系统/run_booking.py`
2. **点击运行按钮** ▶️
3. **选择登录方式**：
   - 输入 `1` - 使用配置文件中的Token
   - 输入 `2` - 手动输入新Token
   - 输入 `3` - 手动输入微信授权码

### 方法3：使用 main.py（交互模式）

1. **在VSCode中打开** `医院抢号系统/main.py`
2. **点击运行按钮** ▶️
3. **程序会自动进入交互模式**，按提示操作

## 📋 内置Token信息

所有脚本都已经内置了你最新提供的token：
```
eJwB0AAv_yijbdqPKfeRDIeUqwBq8xecQAFkyVvq530iQvKbp_mlLCXYTuu90SuKeL3D-yYavAB_vk2gq1ShnfEd6QZ3yhEbqrSp95h047wOVXKX-wsxJ3zh0D8wBY8xgRaHTNyWeMRaKvKE2Yu84HD-JJ-lXS5uTL11RwLWD9mDNxQBzAKEryw7VGv70ooFRdSsgFZP8QjXf2IKcxDzFYI5H2g6ZecmR_m8oHAyrEhzs1JtyC2KnMB-Pem1jcmV91jKR6j1K5CDXa-nfkLqDJiYnqHkJEnPO7tninA=
```

## 🎯 推荐流程

### 第一次使用：
1. 运行 `vscode_run.py` 
2. 选择 `3` 进行测试
3. 确认所有功能正常

### 正式抢号：
1. 运行 `vscode_run.py`
2. 选择 `1` 立即抢号，或选择 `2` 定时抢号

## 🔧 配置信息

当前配置：
- **目标医生**：姜淑君
- **目标日期**：2025-08-05  
- **目标时段**：上午
- **首选时间**：08:30-09:30, 09:30-10:30
- **抢号间隔**：300ms（已优化）

## ⚡ 快速抢号

如果你想立即抢号：
1. 打开 `vscode_run.py`
2. 点击运行 ▶️
3. 输入 `1`
4. 等待结果

## 🔄 如果Token过期

如果提示Token验证失败：
1. 使用Charles重新抓包获取新的token
2. 修改脚本中的 `latest_token` 变量
3. 或者使用交互模式手动输入新token

## 📞 常见问题

**Q: 点击运行没有反应？**
A: 确保在VSCode中打开了正确的Python文件，并且Python环境正确

**Q: 提示找不到模块？**
A: 确保在正确的虚拟环境中，并且已安装依赖：`pip install -r requirements.txt`

**Q: Token验证失败？**
A: Token可能已过期，需要重新获取或使用微信授权码

## 🎉 优势

- ✅ **一键运行** - 直接在VSCode中点击运行
- ✅ **自动配置** - 无需手动设置路径
- ✅ **内置Token** - 已经包含最新token
- ✅ **交互友好** - 清晰的提示和选择
- ✅ **错误处理** - 完善的异常处理机制

现在你可以直接在VSCode中愉快地使用抢号系统了！🎯
