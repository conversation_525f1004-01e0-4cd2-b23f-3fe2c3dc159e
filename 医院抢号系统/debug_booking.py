#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医院抢号系统调试版本
专门用于调试知情同意确认失败的问题
"""

import os
import sys
import json
from hospital_booking import HospitalBooking

def debug_patient_data(booking):
    """调试就诊人数据"""
    print("\n🔍 调试就诊人数据...")
    print("-" * 40)
    
    patients = booking.get_patient_list()
    if not patients:
        print("❌ 获取就诊人列表失败")
        return None
    
    print(f"✅ 获取到 {len(patients)} 个就诊人")
    
    for i, patient in enumerate(patients):
        print(f"\n就诊人 {i+1}:")
        print(f"  完整数据: {json.dumps(patient, ensure_ascii=False, indent=2)}")
        
        # 尝试不同的ID字段
        possible_ids = ['id', 'patientId', 'userId', 'jzrid', 'ID']
        patient_id = None
        
        for id_field in possible_ids:
            if id_field in patient and patient[id_field]:
                patient_id = patient[id_field]
                print(f"  找到ID字段 '{id_field}': {patient_id}")
                break
        
        if not patient_id:
            print("  ❌ 未找到有效的ID字段")
        
        name = patient.get('name', patient.get('patientName', 'Unknown'))
        print(f"  姓名: {name}")
    
    return patients

def debug_schedule_data(booking):
    """调试排班数据"""
    print("\n🔍 调试排班数据...")
    print("-" * 40)
    
    slot_info = booking.find_available_slot()
    if not slot_info:
        print("❌ 未找到可用时间段")
        return None
    
    schedule_code, part_time_id, time_slot = slot_info
    print(f"✅ 找到可用时间段:")
    print(f"  时间段: {time_slot}")
    print(f"  排班码: {schedule_code}")
    print(f"  时段ID: {part_time_id}")
    
    return slot_info

def debug_consent_step(booking, schedule_code):
    """调试知情同意确认步骤"""
    print("\n🔍 调试知情同意确认...")
    print("-" * 40)
    
    print(f"排班码: {schedule_code}")
    print(f"用户ID: {booking.config['user_info']['user_id']}")
    
    # 手动构造请求数据
    data = {
        'scheduleCode': schedule_code,
        'userid': booking.config['user_info']['user_id']
    }
    
    print(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
    print(f"请求URL: {booking.config['api_urls']['base_url']}{booking.config['api_urls']['consent']}")
    
    # 发送请求
    print("\n发送请求...")
    result = booking.make_request(booking.config['api_urls']['consent'], data)
    
    if result:
        print(f"✅ 收到响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
        success = result.get('success', False)
        if success:
            print("✅ 知情同意确认成功")
        else:
            print("❌ 知情同意确认失败")
            print(f"错误信息: {result.get('msg', '无错误信息')}")
    else:
        print("❌ 请求失败，无响应")
    
    return result

def main():
    print("🏥 医院抢号系统 - 调试版本")
    print("=" * 50)
    
    # 加载配置
    script_dir = os.path.dirname(os.path.abspath(__file__))
    config_file = os.path.join(script_dir, 'config.json')
    
    try:
        booking = HospitalBooking(config_file)
        print("✅ 配置文件加载成功")
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return
    
    # 使用最新token
    latest_token = "eJwB0AAv_yijbdqPKfeRDIeUqwBq8xecQAFkyVvq530iQvKbp_mlLCXYTuu90SuKeL3D-yYavAB_vk2gq1ShnfEd6QZ3yhEbqrSp95h047wOVXKX-wsxJ3zh0D8wBY8xgRaHTNyWeMRaKvKE2Yu84HD-JJ-lXS5uTL11RwLWD9mDNxQBzAKEryw7VGv70ooFRdSsgFZP8QjXf2IKcxDzFYI5H2g6ZecmR_m8oHAyrEhzs1JtyC2KnMB-Pem1jcmV91jKR6j1K5CDXa-nfkLqDJiYnqHkJEnPO7tninA="
    
    if not booking.use_existing_token(latest_token):
        print("❌ Token验证失败")
        return
    
    print("✅ Token验证成功")
    
    # 步骤1：调试就诊人数据
    patients = debug_patient_data(booking)
    if not patients:
        return
    
    # 步骤2：调试排班数据
    slot_info = debug_schedule_data(booking)
    if not slot_info:
        return
    
    schedule_code, part_time_id, time_slot = slot_info
    
    # 步骤3：调试知情同意确认
    result = debug_consent_step(booking, schedule_code)
    
    print("\n" + "=" * 50)
    print("调试完成！")
    
    if result and result.get('success'):
        print("🎉 知情同意确认成功，可以继续抢号流程")
    else:
        print("❌ 知情同意确认失败，需要进一步分析")
        
        if result:
            print(f"失败原因: {result.get('msg', '未知错误')}")
            print("可能的解决方案:")
            print("1. 检查排班码是否正确")
            print("2. 检查用户ID是否正确") 
            print("3. 检查是否需要在特定时间才能确认")
            print("4. 检查是否需要额外的参数")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
