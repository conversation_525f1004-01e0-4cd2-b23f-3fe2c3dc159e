#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医院抢号系统主程序
"""

import sys
import argparse
from hospital_booking import HospitalBooking

def main():
    parser = argparse.ArgumentParser(description='医院抢号系统')
    parser.add_argument('--config', default='config.json', help='配置文件路径')
    parser.add_argument('--mode', choices=['test', 'schedule', 'manual'], 
                       default='manual', help='运行模式')
    parser.add_argument('--wx-code', help='微信授权码（手动模式需要）')
    
    args = parser.parse_args()
    
    try:
        booking = HospitalBooking(args.config)
        
        if args.mode == 'test':
            # 测试模式：检查配置和网络连接
            print("=== 测试模式 ===")
            if args.wx_code:
                if booking.wx_auth_login(args.wx_code):
                    print("✓ 微信登录成功")
                    
                    # 测试获取医生排班
                    schedules = booking.get_doctor_schedule()
                    if schedules:
                        print("✓ 获取医生排班成功")
                        print(f"找到 {len(schedules)} 个医院的排班信息")
                    else:
                        print("✗ 获取医生排班失败")
                        
                    # 测试获取就诊人列表
                    patients = booking.get_patient_list()
                    if patients:
                        print("✓ 获取就诊人列表成功")
                        print(f"找到 {len(patients)} 个就诊人")
                        for patient in patients:
                            print(f"  - {patient.get('name', 'Unknown')}")
                    else:
                        print("✗ 获取就诊人列表失败")
                        
                    # 测试查找可用时间段
                    slot_info = booking.find_available_slot()
                    if slot_info:
                        schedule_code, part_time_id, time_slot = slot_info
                        print(f"✓ 找到可用时间段: {time_slot}")
                    else:
                        print("✗ 未找到可用时间段")
                else:
                    print("✗ 微信登录失败")
            else:
                print("测试模式需要提供微信授权码 --wx-code")
                
        elif args.mode == 'schedule':
            # 定时模式：设置定时任务
            print("=== 定时抢号模式 ===")
            if args.wx_code:
                if booking.wx_auth_login(args.wx_code):
                    print("微信登录成功，开始定时抢号...")
                    booking.schedule_booking()
                else:
                    print("微信登录失败，无法启动定时任务")
                    sys.exit(1)
            else:
                print("定时模式需要提供微信授权码 --wx-code")
                sys.exit(1)
                
        elif args.mode == 'manual':
            # 手动模式：立即执行抢号
            print("=== 手动抢号模式 ===")
            if args.wx_code:
                if booking.wx_auth_login(args.wx_code):
                    print("微信登录成功，开始抢号...")
                    success = booking.run_booking_task()
                    if success:
                        print("🎉 抢号成功！")
                    else:
                        print("😞 抢号失败")
                        sys.exit(1)
                else:
                    print("微信登录失败")
                    sys.exit(1)
            else:
                print("手动模式需要提供微信授权码 --wx-code")
                print("\n获取微信授权码的方法：")
                print("1. 在微信中打开医院小程序")
                print("2. 使用Charles抓包工具")
                print("3. 查找 /Oauth/WxAuthCode 接口的 code 参数")
                sys.exit(1)
                
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
