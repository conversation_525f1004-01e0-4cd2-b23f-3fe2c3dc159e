#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医院抢号系统
功能：自动抢医院挂号
"""

import json
import time
import hashlib
import random
import base64
import requests
import schedule
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Optional, Tuple

class HospitalBooking:
    def __init__(self, config_file: str = "config.json"):
        """初始化抢号系统"""
        self.config = self.load_config(config_file)
        self.session = requests.Session()
        self.token = ""
        self.setup_logging()
        self.setup_session()

        # 如果配置中有预设的token，直接使用
        if 'user_info' in self.config and 'token' in self.config['user_info']:
            self.token = self.config['user_info']['token']
            self.logger.info("使用配置文件中的预设token")
        
    def load_config(self, config_file: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            raise
            
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('booking.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_session(self):
        """设置请求会话"""
        self.session.headers.update(self.config['headers'])
        
    def generate_ukey(self) -> str:
        """生成ukey参数"""
        timestamp = str(int(time.time() * 1000))
        random_str = str(random.randint(100000, 999999))
        return hashlib.md5((timestamp + random_str).encode()).hexdigest()
        
    def make_request(self, endpoint: str, data: Dict = None, method: str = "POST") -> Optional[Dict]:
        """发送HTTP请求"""
        url = self.config['api_urls']['base_url'] + endpoint
        
        # 添加必要的请求头
        headers = {
            'uuid': self.config['user_info']['uuid'],
            'ukey': self.generate_ukey(),
            'uname': self.config['user_info']['phone'],
            'token': self.token
        }
        
        try:
            if method.upper() == "POST":
                response = self.session.post(url, data=data, headers=headers, timeout=15)
            else:
                response = self.session.get(url, params=data, headers=headers, timeout=15)
                
            response.raise_for_status()
            
            # 尝试解析JSON响应
            try:
                result = response.json()
                self.logger.debug(f"API响应: {result}")
                return result
            except json.JSONDecodeError:
                # 如果响应是base64编码的
                try:
                    decoded = base64.b64decode(response.text).decode('utf-8')
                    result = json.loads(decoded)
                    self.logger.debug(f"Base64解码后的响应: {result}")
                    return result
                except:
                    self.logger.error(f"无法解析响应: {response.text}")
                    return None
                    
        except requests.RequestException as e:
            self.logger.error(f"请求失败: {e}")
            return None
            
    def wx_auth_login(self, code: str) -> bool:
        """微信授权登录"""
        data = {
            'code': code,
            'checkIsLogin': ''
        }
        
        result = self.make_request(self.config['api_urls']['wx_auth'], data)
        if result and result.get('success'):
            if 'datainfo' in result and 'token' in result['datainfo']:
                self.token = result['datainfo']['token']
                self.logger.info("微信授权登录成功")
                return True
            else:
                # 可能需要二次验证
                check_login = result.get('checkIsLogin')
                if check_login:
                    data['checkIsLogin'] = check_login
                    result = self.make_request(self.config['api_urls']['wx_auth'], data)
                    if result and result.get('success') and 'datainfo' in result:
                        self.token = result['datainfo']['token']
                        self.logger.info("微信授权登录成功（二次验证）")
                        return True
                        
        self.logger.error("微信授权登录失败")
        return False
        
    def get_doctor_schedule(self) -> Optional[List[Dict]]:
        """获取医生排班信息"""
        data = {
            'yqcode1': '',
            'kscode1': '',
            'doctorCode': self.config['target_doctor']['doctor_code'],
            'scheduleType': '',
            'laterThan17': 'true'
        }
        
        result = self.make_request(self.config['api_urls']['doctor_schedule'], data)
        if result and result.get('success'):
            return result.get('datalist', [])
        return None
        
    def get_time_slots(self, schedule_code: str, clinic_date: str) -> Optional[List[Dict]]:
        """获取具体时间段"""
        data = {
            'hospitalId': self.config['target_doctor']['hospital_id'],
            'doctorCode': self.config['target_doctor']['doctor_code'],
            'clinicDate': clinic_date,
            'deptCode': self.config['target_doctor']['dept_code'],
            'clinicDuration': self.config['booking_config']['target_time_period'],
            'scheduleCode': schedule_code
        }
        
        result = self.make_request(self.config['api_urls']['time_slots'], data)
        if result and result.get('success'):
            return result.get('datalist', [])
        return None
        
    def get_patient_list(self) -> Optional[List[Dict]]:
        """获取就诊人列表"""
        data = {
            'userid': self.config['user_info']['user_id']
        }
        
        result = self.make_request(self.config['api_urls']['patient_list'], data)
        if result and result.get('success'):
            return result.get('datalist', [])
        return None
        
    def consent_confirm(self, schedule_code: str) -> bool:
        """知情同意确认"""
        self.logger.info(f"进入知情同意确认函数，schedule_code: {schedule_code}")

        data = {
            'scheduleCode': schedule_code,
            'userid': self.config['user_info']['user_id']
        }

        self.logger.debug(f"知情同意确认请求数据: {data}")
        result = self.make_request(self.config['api_urls']['consent'], data)

        if result:
            self.logger.debug(f"知情同意确认响应: {result}")
            success = result.get('success', False)
            if not success:
                self.logger.warning(f"知情同意确认失败，响应: {result}")
            return success
        else:
            self.logger.error("知情同意确认请求失败，无响应")
            return False
        
    def get_verification_code(self) -> Optional[str]:
        """获取验证码"""
        data = {
            'phone': self.config['user_info']['phone']
        }
        
        result = self.make_request(self.config['api_urls']['get_code'], data)
        if result and result.get('success'):
            # 这里需要用户手动输入验证码
            return input("请输入收到的验证码: ")
        return None
        
    def submit_order(self, schedule_code: str, part_time_id: str, patient_id: str, verification_code: str) -> bool:
        """提交挂号订单"""
        data = {
            'scheduleCode': schedule_code,
            'partTimeId': part_time_id,
            'patientId': patient_id,
            'verificationCode': verification_code,
            'hospitalId': self.config['target_doctor']['hospital_id'],
            'doctorCode': self.config['target_doctor']['doctor_code'],
            'deptCode': self.config['target_doctor']['dept_code']
        }

        result = self.make_request(self.config['api_urls']['add_order'], data)
        return result and result.get('success', False)

    def use_existing_token(self, token: str) -> bool:
        """直接使用已有的token"""
        self.token = token
        self.logger.info("设置token成功")

        # 验证token是否有效，尝试获取医生列表
        try:
            schedules = self.get_doctor_schedule()
            if schedules is not None:
                self.logger.info("token验证成功，可以正常使用")
                return True
            else:
                self.logger.warning("token可能已过期或无效")
                return False
        except Exception as e:
            self.logger.error(f"token验证失败: {e}")
            return False

    def find_available_slot(self) -> Optional[Tuple[str, str, str]]:
        """查找可用的时间段"""
        # 获取医生排班
        schedules = self.get_doctor_schedule()
        if not schedules:
            self.logger.error("获取医生排班失败")
            return None

        target_date = self.config['booking_config']['target_date']
        target_period = self.config['booking_config']['target_time_period']

        # 查找目标日期的排班
        target_schedule = None
        for hospital_data in schedules:
            for schedule in hospital_data.get('schedule', []):
                if (schedule.get('clinicDate') == target_date and
                    schedule.get('clinicDuration') == target_period and
                    schedule.get('yystatus') == '可约'):
                    target_schedule = schedule
                    break
            if target_schedule:
                break

        if not target_schedule:
            self.logger.warning(f"未找到{target_date} {target_period}的可约排班")
            return None

        schedule_code = target_schedule['schedulecode']
        self.logger.info(f"找到可约排班: {schedule_code}")

        # 获取具体时间段
        time_slots = self.get_time_slots(schedule_code, target_date)
        if not time_slots:
            self.logger.error("获取时间段失败")
            return None

        # 查找有余号的时间段
        preferred_slots = self.config['booking_config']['preferred_time_slots']
        for slot in time_slots:
            if (slot.get('leavecount', 0) > 0 and
                slot.get('shijianduan') in preferred_slots):
                self.logger.info(f"找到可用时间段: {slot['shijianduan']}, 余号: {slot['leavecount']}")
                return schedule_code, slot['parttimeid'], slot['shijianduan']

        # 如果首选时间段没有，选择任何有余号的时间段
        for slot in time_slots:
            if slot.get('leavecount', 0) > 0:
                self.logger.info(f"找到可用时间段: {slot['shijianduan']}, 余号: {slot['leavecount']}")
                return schedule_code, slot['parttimeid'], slot['shijianduan']

        self.logger.warning("没有找到可用的时间段")
        return None

    def book_appointment(self) -> bool:
        """执行抢号"""
        self.logger.info("开始执行抢号...")

        # 查找可用时间段
        slot_info = self.find_available_slot()
        if not slot_info:
            return False

        schedule_code, part_time_id, time_slot = slot_info

        # 获取就诊人信息
        patients = self.get_patient_list()
        if not patients:
            self.logger.error("获取就诊人列表失败")
            return False

        self.logger.debug(f"就诊人数据: {patients}")

        # 安全获取就诊人ID
        try:
            patient_id = patients[0].get('id') or patients[0].get('patientId') or patients[0].get('userId')
            patient_name = patients[0].get('name', 'Unknown')
            self.logger.info(f"使用就诊人: {patient_name}, ID: {patient_id}")

            if not patient_id:
                self.logger.error(f"无法获取就诊人ID，数据结构: {patients[0]}")
                return False

        except (IndexError, KeyError, TypeError) as e:
            self.logger.error(f"解析就诊人数据失败: {e}, 数据: {patients}")
            return False

        # 知情同意确认
        self.logger.info(f"开始知情同意确认，schedule_code: {schedule_code}")
        if not self.consent_confirm(schedule_code):
            self.logger.error("知情同意确认失败")
            return False

        # 获取验证码
        verification_code = self.get_verification_code()
        if not verification_code:
            self.logger.error("获取验证码失败")
            return False

        # 提交订单
        success = self.submit_order(schedule_code, part_time_id, patient_id, verification_code)
        if success:
            self.logger.info(f"抢号成功！时间段: {time_slot}")
            return True
        else:
            self.logger.error("提交订单失败")
            return False

    def run_booking_task(self):
        """运行抢号任务"""
        max_retry = self.config['booking_config']['max_retry_times']
        retry_interval = self.config['booking_config']['retry_interval']

        for attempt in range(max_retry):
            self.logger.info(f"第 {attempt + 1} 次尝试抢号...")

            if self.book_appointment():
                self.logger.info("抢号成功，任务完成！")
                return True

            if attempt < max_retry - 1:
                self.logger.info(f"等待 {retry_interval} 秒后重试...")
                time.sleep(retry_interval)

        self.logger.error("抢号失败，已达到最大重试次数")
        return False

    def schedule_booking(self):
        """定时抢号"""
        booking_time = self.config['booking_config']['booking_time']
        advance_seconds = self.config['booking_config']['advance_seconds']

        # 计算实际执行时间（提前几秒）
        target_time = datetime.strptime(booking_time, "%H:%M:%S").time()
        execute_time = (datetime.combine(datetime.today(), target_time) -
                       timedelta(seconds=advance_seconds)).time()

        schedule.every().day.at(execute_time.strftime("%H:%M:%S")).do(self.run_booking_task)

        self.logger.info(f"已设置定时任务，将在 {execute_time} 开始抢号")
        self.logger.info("等待执行时间到达...")

        while True:
            schedule.run_pending()
            time.sleep(1)
