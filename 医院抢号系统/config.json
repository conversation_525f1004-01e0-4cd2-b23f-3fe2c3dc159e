{"user_info": {"phone": "13971563836", "user_id": "46D5EFD4-3F8E-41ED-B4C3-EF2F8B297CDD", "uuid": "oqrz4jiS2A2OMaAbk5p7RX9fHd9M"}, "target_doctor": {"doctor_code": "102304", "doctor_name": "姜淑君", "dept_code": "0204100", "dept_name": "中医内科门诊", "hospital_id": "270018"}, "booking_config": {"target_date": "2025-08-05", "target_time_period": "上午", "preferred_time_slots": ["08:30-09:30", "09:30-10:30"], "booking_time": "17:00:00", "advance_seconds": 5, "max_retry_times": 10, "retry_interval": 0.1}, "headers": {"User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.60(0x18003c32) NetType/WIFI Language/zh_CN", "Accept": "application/json", "Accept-Language": "zh-<PERSON><PERSON>,zh-<PERSON>;q=0.9", "Accept-Encoding": "gzip, deflate, br", "Origin": "https://tjhapp.com.cn", "Referer": "https://tjhapp.com.cn/", "X-Requested-With": "XMLHttpRequest", "Content-Type": "application/x-www-form-urlencoded", "Connection": "keep-alive", "plan": "wxapp"}, "api_urls": {"base_url": "https://tjhapp.com.cn:8013", "login": "/patient/userlogin2", "wx_auth": "/Oauth/WxAuthCode", "my_doctors": "/TjPatientInfoMydoctor/getMydoctorList", "doctor_status": "/KsYs/getDoctorStatus", "doctor_schedule": "/yuyue/getdocinfoNewV2", "time_slots": "/yuyue/gethy_new", "patient_list": "/patientjzrV2/getjzrNew", "consent": "/guahao/add/consent", "get_code": "/user/getzhcode", "add_order": "/yuyue/addorder"}}