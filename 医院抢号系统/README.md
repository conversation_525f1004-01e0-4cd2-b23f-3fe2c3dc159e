# 医院抢号系统

基于Python开发的医院微信小程序自动抢号工具。

## ⚠️ 重要声明

**本工具仅供学习和研究使用，请勿用于商业用途或违法行为。使用本工具可能违反医院服务条款，请自行承担风险。**

## 功能特性

- 🔐 微信授权登录
- 👨‍⚕️ 自动获取医生排班信息
- ⏰ 定时抢号功能
- 🔄 失败重试机制
- 📝 详细日志记录
- ⚙️ 灵活的配置选项

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置说明

编辑 `config.json` 文件，配置以下信息：

### 用户信息
- `phone`: 手机号
- `user_id`: 用户ID（从抓包数据中获取）
- `uuid`: 微信用户唯一标识（从抓包数据中获取）

### 目标医生信息
- `doctor_code`: 医生代码
- `doctor_name`: 医生姓名
- `dept_code`: 科室代码
- `dept_name`: 科室名称
- `hospital_id`: 医院ID

### 抢号配置
- `target_date`: 目标日期（格式：2025-08-05）
- `target_time_period`: 时间段（上午/下午）
- `preferred_time_slots`: 首选时间段列表
- `booking_time`: 抢号时间（格式：17:00:00）
- `advance_seconds`: 提前执行秒数
- `max_retry_times`: 最大重试次数
- `retry_interval`: 重试间隔（秒）

## 使用方法

### 1. 获取微信授权码

使用Charles抓包工具：
1. 在iPhone上打开医院微信小程序
2. 使用Charles抓包
3. 查找 `/Oauth/WxAuthCode` 接口
4. 复制请求参数中的 `code` 值

### 2. 测试模式

验证配置和网络连接：

```bash
python main.py --mode test --wx-code YOUR_WX_CODE
```

### 3. 手动抢号

立即执行抢号：

```bash
python main.py --mode manual --wx-code YOUR_WX_CODE
```

### 4. 定时抢号

设置定时任务：

```bash
python main.py --mode schedule --wx-code YOUR_WX_CODE
```

## 工作流程

1. **微信授权登录** - 使用微信授权码获取访问token
2. **获取医生排班** - 查询目标医生的排班信息
3. **查找可用时间段** - 寻找有余号的时间段
4. **获取就诊人信息** - 获取绑定的就诊人列表
5. **知情同意确认** - 确认知情同意书
6. **获取验证码** - 获取手机验证码
7. **提交挂号订单** - 完成挂号

## 注意事项

1. **微信授权码有时效性**，通常几分钟内失效，需要及时使用
2. **定时抢号建议提前5-10秒执行**，避免网络延迟
3. **验证码需要手动输入**，程序会提示输入
4. **请控制请求频率**，避免给医院服务器造成压力
5. **建议在网络环境良好的地方运行**

## 日志文件

程序运行时会生成 `booking.log` 日志文件，记录详细的执行过程。

## 常见问题

### Q: 微信授权码在哪里获取？
A: 使用Charles抓包工具，在微信小程序中操作时抓取 `/Oauth/WxAuthCode` 接口的 `code` 参数。

### Q: 为什么登录失败？
A: 检查微信授权码是否正确，是否已过期，用户信息配置是否正确。

### Q: 为什么找不到可用时间段？
A: 可能是目标日期还未放号，或者已经被抢完，检查配置的日期和时间段是否正确。

### Q: 验证码输入后还是失败？
A: 验证码可能已过期，或者网络延迟导致提交失败，可以重试。

## 免责声明

本工具仅供技术学习和研究使用。使用者应当遵守相关法律法规和医院的服务条款。作者不对使用本工具产生的任何后果承担责任。
