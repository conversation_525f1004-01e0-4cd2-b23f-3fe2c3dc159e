# 获取微信授权码详细指南

## 🎯 目标
获取微信小程序的授权码（code参数），用于程序登录

## 📱 操作步骤

### 步骤1：准备Charles抓包
1. **启动Charles**
2. **设置过滤器**：
   - Proxy → Recording Settings → Include
   - 添加：`tjhapp.com.cn`
3. **确保iPhone已安装证书**

### 步骤2：触发授权码生成

#### 方法A：重新进入小程序（推荐）
1. **完全退出小程序**
   - 在微信中，上滑小程序卡片完全关闭
   - 或者重启微信app

2. **重新搜索进入**
   - 在微信中搜索医院名称
   - 点击进入小程序
   - 这时会自动触发授权流程

#### 方法B：清除小程序数据
1. **删除小程序**
   - 在微信"发现"→"小程序"中找到医院小程序
   - 长按选择"删除"

2. **重新添加**
   - 搜索医院小程序
   - 重新进入

#### 方法C：切换网络环境
1. **切换网络**
   - 从WiFi切换到4G，或反之
   - 重新进入小程序

### 步骤3：在Charles中查找授权码

1. **查找接口**
   - 在Charles中找到 `tjhapp.com.cn:8013/Oauth/WxAuthCode`
   - 这个接口会在进入小程序时自动调用

2. **提取code参数**
   - 点击该请求
   - 查看"Request"标签页
   - 在"Form"或"Raw"中找到 `code=xxxxxx`
   - 复制这个code值

### 步骤4：验证授权码
授权码通常是这样的格式：
```
093kng1w3Jekp535aZ3w32aGwJ0kng1u
```
- 长度约32个字符
- 包含字母和数字

## ⚡ 快速使用

### 方法1：使用新获取的授权码
```bash
python3 main.py --mode manual --wx-code YOUR_NEW_CODE
```

### 方法2：使用已有token（推荐）
```bash
python3 main.py --mode token
```

## 🔧 故障排除

### 问题1：找不到WxAuthCode接口
**原因**：可能没有触发授权流程
**解决**：
- 确保完全退出小程序后重新进入
- 检查Charles过滤器设置
- 尝试切换网络环境

### 问题2：授权码无效
**原因**：授权码有时效性（通常2-5分钟）
**解决**：
- 获取后立即使用
- 重新获取新的授权码

### 问题3：抓不到请求
**原因**：Charles配置问题
**解决**：
- 检查iPhone证书安装
- 确认Charles代理设置
- 重启Charles和iPhone

## 💡 小贴士

1. **最佳时机**：在需要抢号前5分钟内获取授权码
2. **备用方案**：可以使用配置文件中的token（如果还有效）
3. **多次尝试**：如果一次获取失败，可以多试几次
4. **网络环境**：在稳定的网络环境下操作

## 🚀 推荐流程

1. **先测试token模式**：
   ```bash
   python3 main.py --mode token
   ```

2. **如果token失效，再获取新授权码**：
   - 按上述步骤获取授权码
   - 使用新授权码运行程序

3. **定时抢号**：
   ```bash
   python3 main.py --mode schedule --wx-code YOUR_CODE
   ```
