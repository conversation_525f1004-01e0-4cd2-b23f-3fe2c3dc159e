#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抢号系统测试脚本
"""

import json
from hospital_booking import HospitalBooking

def test_config():
    """测试配置文件加载"""
    print("=== 测试配置文件 ===")
    try:
        booking = HospitalBooking()
        print("✓ 配置文件加载成功")
        print(f"目标医生: {booking.config['target_doctor']['doctor_name']}")
        print(f"目标日期: {booking.config['booking_config']['target_date']}")
        print(f"目标时段: {booking.config['booking_config']['target_time_period']}")
        return True
    except Exception as e:
        print(f"✗ 配置文件加载失败: {e}")
        return False

def test_ukey_generation():
    """测试ukey生成"""
    print("\n=== 测试ukey生成 ===")
    try:
        booking = HospitalBooking()
        ukey1 = booking.generate_ukey()
        ukey2 = booking.generate_ukey()
        
        print(f"生成的ukey1: {ukey1}")
        print(f"生成的ukey2: {ukey2}")
        
        if len(ukey1) == 32 and len(ukey2) == 32 and ukey1 != ukey2:
            print("✓ ukey生成正常")
            return True
        else:
            print("✗ ukey生成异常")
            return False
    except Exception as e:
        print(f"✗ ukey生成失败: {e}")
        return False

def test_network_connection():
    """测试网络连接"""
    print("\n=== 测试网络连接 ===")
    try:
        booking = HospitalBooking()
        
        # 测试基础网络连接
        import requests
        response = requests.get("https://tjhapp.com.cn", timeout=5)
        if response.status_code == 200:
            print("✓ 网络连接正常")
            return True
        else:
            print(f"✗ 网络连接异常，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 网络连接失败: {e}")
        return False

def test_with_real_data():
    """使用真实数据测试（需要微信授权码）"""
    print("\n=== 真实数据测试 ===")
    print("此测试需要微信授权码，请按以下步骤获取：")
    print("1. 在iPhone上打开医院微信小程序")
    print("2. 使用Charles抓包")
    print("3. 查找 /Oauth/WxAuthCode 接口的 code 参数")
    print()
    
    wx_code = input("请输入微信授权码（直接回车跳过）: ").strip()
    if not wx_code:
        print("跳过真实数据测试")
        return True
        
    try:
        booking = HospitalBooking()
        
        # 测试登录
        if booking.wx_auth_login(wx_code):
            print("✓ 微信登录成功")
            
            # 测试获取医生排班
            schedules = booking.get_doctor_schedule()
            if schedules:
                print(f"✓ 获取医生排班成功，找到 {len(schedules)} 个医院")
                
                # 测试查找可用时间段
                slot_info = booking.find_available_slot()
                if slot_info:
                    schedule_code, part_time_id, time_slot = slot_info
                    print(f"✓ 找到可用时间段: {time_slot}")
                else:
                    print("⚠ 当前没有可用时间段")
                    
                # 测试获取就诊人列表
                patients = booking.get_patient_list()
                if patients:
                    print(f"✓ 获取就诊人列表成功，找到 {len(patients)} 个就诊人")
                    for patient in patients:
                        print(f"  - {patient.get('name', 'Unknown')}")
                else:
                    print("✗ 获取就诊人列表失败")
                    
                return True
            else:
                print("✗ 获取医生排班失败")
                return False
        else:
            print("✗ 微信登录失败")
            return False
            
    except Exception as e:
        print(f"✗ 真实数据测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("医院抢号系统测试")
    print("=" * 50)
    
    tests = [
        ("配置文件测试", test_config),
        ("ukey生成测试", test_ukey_generation),
        ("网络连接测试", test_network_connection),
        ("真实数据测试", test_with_real_data)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}执行异常: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    
    passed = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过，系统准备就绪！")
    else:
        print("⚠ 部分测试失败，请检查配置和网络环境")

if __name__ == "__main__":
    main()
