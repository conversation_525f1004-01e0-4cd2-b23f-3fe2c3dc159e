#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医院抢号系统 - VSCode直接运行版本
点击VSCode的运行按钮即可使用
"""

import os
import sys
from hospital_booking import HospitalBooking

def main():
    print("🏥 医院抢号系统")
    print("=" * 50)
    
    # 自动查找配置文件
    script_dir = os.path.dirname(os.path.abspath(__file__))
    config_file = os.path.join(script_dir, 'config.json')
    
    try:
        booking = HospitalBooking(config_file)
        print("✓ 配置文件加载成功")
    except Exception as e:
        print(f"✗ 配置文件加载失败: {e}")
        return
    
    # 显示当前配置
    print(f"目标医生: {booking.config['target_doctor']['doctor_name']}")
    print(f"目标日期: {booking.config['booking_config']['target_date']}")
    print(f"目标时段: {booking.config['booking_config']['target_time_period']}")
    print()
    
    # 登录选择
    print("请选择登录方式：")
    print("1. 使用配置文件中的Token（推荐）")
    print("2. 手动输入新Token")
    print("3. 手动输入微信授权码")
    
    choice = input("请输入选择 (1/2/3): ").strip()
    
    login_success = False
    
    if choice == "1":
        # 使用配置文件中的token
        if booking.token:
            print("✓ 使用配置文件中的token")
            # 验证token是否有效
            try:
                schedules = booking.get_doctor_schedule()
                if schedules is not None:
                    print("✓ Token验证成功")
                    login_success = True
                else:
                    print("✗ Token可能已过期，请选择其他登录方式")
            except Exception as e:
                print(f"✗ Token验证失败: {e}")
        else:
            print("✗ 配置文件中没有token")
            
    elif choice == "2":
        # 手动输入token
        print("\n请输入Token:")
        print("提示：这是你最新的token")
        print("eJwB0AAv_yijbdqPKfeRDIeUqwBq8xecQAFkyVvq530iQvKbp_mlLCXYTuu90SuKeL3D-yYavAB_vk2gq1ShnfEd6QZ3yhEbqrSp95h047wOVXKX-wsxJ3zh0D8wBY8xgRaHTNyWeMRaKvKE2Yu84HD-JJ-lXS5uTL11RwLWD9mDNxQBzAKEryw7VGv70ooFRdSsgFZP8QjXf2IKcxDzFYI5H2g6ZecmR_m8oHAyrEhzs1JtyC2KnMB-Pem1jcmV91jKR6j1K5CDXa-nfkLqDJiYnqHkJEnPO7tninA=")
        print()
        token = input("Token (直接回车使用上面的token): ").strip()
        
        if not token:
            # 使用默认token
            token = "eJwB0AAv_yijbdqPKfeRDIeUqwBq8xecQAFkyVvq530iQvKbp_mlLCXYTuu90SuKeL3D-yYavAB_vk2gq1ShnfEd6QZ3yhEbqrSp95h047wOVXKX-wsxJ3zh0D8wBY8xgRaHTNyWeMRaKvKE2Yu84HD-JJ-lXS5uTL11RwLWD9mDNxQBzAKEryw7VGv70ooFRdSsgFZP8QjXf2IKcxDzFYI5H2g6ZecmR_m8oHAyrEhzs1JtyC2KnMB-Pem1jcmV91jKR6j1K5CDXa-nfkLqDJiYnqHkJEnPO7tninA="
            
        if booking.use_existing_token(token):
            print("✓ Token验证成功")
            login_success = True
        else:
            print("✗ Token验证失败")
            
    elif choice == "3":
        # 手动输入微信授权码
        print("\n请输入微信授权码:")
        print("（从Charles抓包的 /Oauth/WxAuthCode 接口获取 code 参数）")
        wx_code = input("微信授权码: ").strip()
        if wx_code:
            if booking.wx_auth_login(wx_code):
                print("✓ 微信登录成功")
                login_success = True
            else:
                print("✗ 微信登录失败")
        else:
            print("微信授权码不能为空")
            
    else:
        print("无效的选择")
        return
        
    if not login_success:
        print("登录失败，程序退出")
        return
        
    print("\n" + "=" * 50)
    print("请选择操作：")
    print("1. 立即抢号")
    print("2. 测试功能")
    print("3. 定时抢号")
    
    action = input("请输入选择 (1/2/3): ").strip()
    
    if action == "1":
        print("\n🚀 开始立即抢号...")
        print("=" * 30)
        success = booking.run_booking_task()
        if success:
            print("\n🎉 抢号成功！")
        else:
            print("\n😞 抢号失败")
            
    elif action == "2":
        print("\n🔧 开始测试功能...")
        print("=" * 30)
        
        # 测试获取医生排班
        print("正在获取医生排班...")
        schedules = booking.get_doctor_schedule()
        if schedules:
            print("✓ 获取医生排班成功")
            print(f"找到 {len(schedules)} 个医院的排班信息")
        else:
            print("✗ 获取医生排班失败")
            
        # 测试获取就诊人列表
        print("正在获取就诊人列表...")
        patients = booking.get_patient_list()
        if patients:
            print("✓ 获取就诊人列表成功")
            print(f"找到 {len(patients)} 个就诊人")
            for i, patient in enumerate(patients, 1):
                print(f"  {i}. {patient.get('name', 'Unknown')}")
        else:
            print("✗ 获取就诊人列表失败")
            
        # 测试查找可用时间段
        print("正在查找可用时间段...")
        slot_info = booking.find_available_slot()
        if slot_info:
            _, _, time_slot = slot_info
            print(f"✓ 找到可用时间段: {time_slot}")
        else:
            print("✗ 未找到可用时间段")
            
        print("\n测试完成！")
        
    elif action == "3":
        print("\n⏰ 开始定时抢号...")
        print("=" * 30)
        booking_time = booking.config['booking_config']['booking_time']
        print(f"将在 {booking_time} 开始抢号")
        print("程序将保持运行，等待执行时间...")
        print("按 Ctrl+C 可以停止")
        try:
            booking.schedule_booking()
        except KeyboardInterrupt:
            print("\n定时任务已停止")
            
    else:
        print("无效的选择")
        
    print("\n程序结束")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n程序运行出错: {e}")
    
    input("\n按回车键退出...")  # 防止VSCode窗口立即关闭
