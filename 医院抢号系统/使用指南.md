# 医院抢号系统使用指南

## 🚀 快速开始

### 1. 安装依赖
```bash
cd 医院抢号系统
pip install -r requirements.txt
```

### 2. 分析抓包数据
```bash
python3 har_analyzer.py 公众号挂号.har
```
这会分析你的抓包数据并显示关键信息。

### 3. 更新配置文件
根据分析结果，编辑 `config.json` 文件：
- 确认用户信息（phone, user_id, uuid）
- 设置目标医生信息
- 配置抢号时间和策略

### 4. 运行测试
```bash
python3 test_booking.py
```
验证配置和网络连接是否正常。

### 5. 开始抢号

#### 测试模式（推荐先运行）
```bash
python3 main.py --mode test --wx-code YOUR_WX_CODE
```

#### 手动抢号
```bash
python3 main.py --mode manual --wx-code YOUR_WX_CODE
```

#### 定时抢号
```bash
python3 main.py --mode schedule --wx-code YOUR_WX_CODE
```

## 📋 详细步骤

### 步骤1：获取微信授权码

1. **准备工作**
   - 确保iPhone已安装Charles证书
   - 启动Charles抓包
   - 设置过滤器：`tjhapp.com.cn`

2. **获取授权码**
   - 在iPhone上打开医院微信小程序
   - 进行登录操作
   - 在Charles中找到 `/Oauth/WxAuthCode` 接口
   - 复制请求参数中的 `code` 值

3. **注意事项**
   - 授权码有效期很短（通常2-5分钟）
   - 需要在获取后立即使用
   - 每次登录都会生成新的授权码

### 步骤2：配置目标信息

编辑 `config.json` 文件：

```json
{
  "target_doctor": {
    "doctor_code": "102304",        // 医生代码
    "doctor_name": "姜淑君",        // 医生姓名
    "dept_code": "0204100",         // 科室代码
    "dept_name": "中医内科门诊",    // 科室名称
    "hospital_id": "270018"         // 医院ID
  },
  "booking_config": {
    "target_date": "2025-08-05",    // 目标日期
    "target_time_period": "上午",   // 上午/下午
    "preferred_time_slots": [       // 首选时间段
      "08:30-09:30",
      "09:30-10:30"
    ],
    "booking_time": "17:00:00",     // 抢号时间
    "advance_seconds": 5,           // 提前执行秒数
    "max_retry_times": 10,          // 最大重试次数
    "retry_interval": 0.1           // 重试间隔（秒）
  }
}
```

### 步骤3：执行抢号策略

#### 策略1：提前测试
在正式抢号前，建议先运行测试模式：
```bash
python3 main.py --mode test --wx-code YOUR_WX_CODE
```

测试内容包括：
- ✅ 微信登录是否成功
- ✅ 能否获取医生排班
- ✅ 能否获取就诊人信息
- ✅ 是否有可用时间段

#### 策略2：手动抢号
适合立即执行的场景：
```bash
python3 main.py --mode manual --wx-code YOUR_WX_CODE
```

#### 策略3：定时抢号
适合在指定时间自动执行：
```bash
python3 main.py --mode schedule --wx-code YOUR_WX_CODE
```

程序会在配置的时间自动开始抢号。

## ⚡ 抢号技巧

### 时间策略
1. **提前准备**：在放号前5-10分钟获取授权码
2. **精确时间**：设置比放号时间提前3-5秒执行
3. **网络环境**：使用稳定的网络环境

### 配置优化
1. **重试设置**：
   - `max_retry_times`: 10-20次
   - `retry_interval`: 0.1-0.2秒

2. **时间段选择**：
   - 设置多个备选时间段
   - 按优先级排序

### 成功率提升
1. **多设备准备**：可以在多台设备上同时运行
2. **备用方案**：准备手动抢号作为备用
3. **监控日志**：实时查看 `booking.log` 文件

## 🔧 故障排除

### 常见问题

#### 1. 微信登录失败
**现象**：提示登录失败或token无效
**解决方案**：
- 检查授权码是否正确
- 确认授权码是否过期
- 重新获取授权码

#### 2. 找不到可用时间段
**现象**：提示"未找到可用时间段"
**解决方案**：
- 检查目标日期是否正确
- 确认是否已经放号
- 检查医生是否有排班

#### 3. 网络请求失败
**现象**：请求超时或连接失败
**解决方案**：
- 检查网络连接
- 确认医院服务器是否正常
- 调整请求超时时间

#### 4. 验证码问题
**现象**：验证码获取失败或输入错误
**解决方案**：
- 确保手机能正常接收短信
- 快速输入验证码
- 检查手机号是否正确

### 日志分析

查看 `booking.log` 文件了解详细执行过程：

```bash
tail -f booking.log
```

关键日志信息：
- `微信登录成功` - 登录状态
- `找到可用时间段` - 发现可约时间
- `抢号成功` - 成功挂号
- `请求失败` - 网络或服务器问题

## ⚠️ 重要提醒

1. **合法使用**：本工具仅供学习研究，请遵守医院服务条款
2. **适度使用**：控制请求频率，避免给服务器造成压力
3. **备份方案**：保留手动抢号的备用方案
4. **及时更新**：医院可能更新接口，需要相应调整代码

## 📞 技术支持

如果遇到问题，可以：
1. 查看日志文件 `booking.log`
2. 运行测试脚本 `test_booking.py`
3. 检查网络和配置
4. 重新抓包分析接口变化

祝你抢号成功！🎉
