#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HAR文件分析工具
用于分析Charles抓包数据，提取关键信息
"""

import json
import base64
from typing import Dict, List

class HARAnalyzer:
    def __init__(self, har_file: str):
        """初始化HAR分析器"""
        with open(har_file, 'r', encoding='utf-8') as f:
            self.har_data = json.load(f)
            
    def decode_base64_response(self, response_text: str) -> Dict:
        """解码base64响应"""
        try:
            decoded = base64.b64decode(response_text).decode('utf-8')
            return json.loads(decoded)
        except:
            return {}
            
    def analyze_login_flow(self):
        """分析登录流程"""
        print("=== 登录流程分析 ===")
        
        for entry in self.har_data['log']['entries']:
            url = entry['request']['url']
            
            if '/Oauth/WxAuthCode' in url:
                print(f"\n微信授权接口: {url}")
                
                # 请求参数
                if 'postData' in entry['request']:
                    params = entry['request']['postData'].get('params', [])
                    for param in params:
                        print(f"  请求参数: {param['name']} = {param['value']}")
                
                # 响应数据
                response_text = entry['response']['content'].get('text', '')
                if response_text:
                    response_data = self.decode_base64_response(response_text)
                    if response_data:
                        print(f"  响应: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
                        
    def analyze_doctor_info(self):
        """分析医生信息"""
        print("\n=== 医生信息分析 ===")
        
        for entry in self.har_data['log']['entries']:
            url = entry['request']['url']
            
            if '/getMydoctorList' in url:
                print(f"\n医生列表接口: {url}")
                response_text = entry['response']['content'].get('text', '')
                if response_text:
                    response_data = self.decode_base64_response(response_text)
                    if response_data and 'datalist' in response_data:
                        for doctor in response_data['datalist']:
                            print(f"  医生: {doctor.get('doctorName')} ({doctor.get('doctorCode')})")
                            print(f"    科室: {doctor.get('deptNames')}")
                            print(f"    职称: {doctor.get('doctortitle')}")
                            
    def analyze_schedule_info(self):
        """分析排班信息"""
        print("\n=== 排班信息分析 ===")
        
        for entry in self.har_data['log']['entries']:
            url = entry['request']['url']
            
            if '/getdocinfoNewV2' in url:
                print(f"\n排班查询接口: {url}")
                
                # 请求参数
                if 'postData' in entry['request']:
                    params = entry['request']['postData'].get('params', [])
                    for param in params:
                        print(f"  请求参数: {param['name']} = {param['value']}")
                
                response_text = entry['response']['content'].get('text', '')
                if response_text:
                    response_data = self.decode_base64_response(response_text)
                    if response_data and 'datalist' in response_data:
                        for hospital in response_data['datalist']:
                            print(f"  医院: {hospital.get('hospitalmc')}")
                            for schedule in hospital.get('schedule', []):
                                print(f"    日期: {schedule.get('clinicDate')}")
                                print(f"    时段: {schedule.get('clinicDuration')}")
                                print(f"    状态: {schedule.get('yystatus')}")
                                print(f"    费用: {schedule.get('sumFee')}")
                                print(f"    排班码: {schedule.get('schedulecode')}")
                                print()
                                
    def analyze_time_slots(self):
        """分析时间段信息"""
        print("\n=== 时间段信息分析 ===")
        
        for entry in self.har_data['log']['entries']:
            url = entry['request']['url']
            
            if '/gethy_new' in url:
                print(f"\n时间段查询接口: {url}")
                
                # 请求参数
                if 'postData' in entry['request']:
                    params = entry['request']['postData'].get('params', [])
                    for param in params:
                        print(f"  请求参数: {param['name']} = {param['value']}")
                
                response_text = entry['response']['content'].get('text', '')
                if response_text:
                    response_data = self.decode_base64_response(response_text)
                    if response_data and 'datalist' in response_data:
                        for slot in response_data['datalist']:
                            print(f"    时间段: {slot.get('shijianduan')}")
                            print(f"    余号: {slot.get('leavecount')}")
                            print(f"    时段ID: {slot.get('parttimeid')}")
                            print()
                            
    def extract_user_info(self) -> Dict:
        """提取用户信息"""
        user_info = {}
        
        for entry in self.har_data['log']['entries']:
            # 从请求头中提取用户信息
            headers = entry['request']['headers']
            for header in headers:
                if header['name'] == 'uuid':
                    user_info['uuid'] = header['value']
                elif header['name'] == 'uname':
                    user_info['phone'] = header['value']
                    
            # 从登录响应中提取用户ID
            if '/WxAuthCode' in entry['request']['url']:
                response_text = entry['response']['content'].get('text', '')
                if response_text:
                    response_data = self.decode_base64_response(response_text)
                    if response_data and 'datainfo' in response_data:
                        datainfo = response_data['datainfo']
                        if 'userid' in datainfo:
                            user_info['user_id'] = datainfo['userid']
                            
        return user_info
        
    def generate_config(self) -> Dict:
        """生成配置文件"""
        user_info = self.extract_user_info()
        
        config = {
            "user_info": user_info,
            "target_doctor": {
                "doctor_code": "102304",
                "doctor_name": "姜淑君",
                "dept_code": "0204100", 
                "dept_name": "中医内科门诊",
                "hospital_id": "270018"
            },
            "booking_config": {
                "target_date": "2025-08-05",
                "target_time_period": "上午",
                "preferred_time_slots": ["08:30-09:30", "09:30-10:30"],
                "booking_time": "17:00:00",
                "advance_seconds": 5,
                "max_retry_times": 10,
                "retry_interval": 0.1
            }
        }
        
        return config

def main():
    import sys
    
    if len(sys.argv) != 2:
        print("用法: python har_analyzer.py <har_file>")
        sys.exit(1)
        
    har_file = sys.argv[1]
    analyzer = HARAnalyzer(har_file)
    
    print("开始分析HAR文件...")
    analyzer.analyze_login_flow()
    analyzer.analyze_doctor_info()
    analyzer.analyze_schedule_info()
    analyzer.analyze_time_slots()
    
    print("\n=== 提取的用户信息 ===")
    user_info = analyzer.extract_user_info()
    print(json.dumps(user_info, ensure_ascii=False, indent=2))
    
    print("\n=== 生成的配置文件 ===")
    config = analyzer.generate_config()
    print(json.dumps(config, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    main()
