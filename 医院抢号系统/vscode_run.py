#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医院抢号系统 - VSCode一键运行版本
直接在VSCode中点击运行按钮即可使用
"""

import os
import sys
from hospital_booking import HospitalBooking

def main():
    print("🏥 医院抢号系统 - VSCode版本")
    print("=" * 50)
    
    # 自动查找配置文件
    script_dir = os.path.dirname(os.path.abspath(__file__))
    config_file = os.path.join(script_dir, 'config.json')
    
    try:
        booking = HospitalBooking(config_file)
        print("✓ 配置文件加载成功")
        print(f"目标医生: {booking.config['target_doctor']['doctor_name']}")
        print(f"目标日期: {booking.config['booking_config']['target_date']}")
        print(f"目标时段: {booking.config['booking_config']['target_time_period']}")
        print()
    except Exception as e:
        print(f"✗ 配置文件加载失败: {e}")
        return
    
    # 使用最新的token
    latest_token = "eJwB0AAv_yijbdqPKfeRDIeUqwBq8xecQAFkyVvq530iQvKbp_mlLCXYTuu90SuKeL3D-yYavAB_vk2gq1ShnfEd6QZ3yhEbqrSp95h047wOVXKX-wsxJ3zh0D8wBY8xgRaHTNyWeMRaKvKE2Yu84HD-JJ-lXS5uTL11RwLWD9mDNxQBzAKEryw7VGv70ooFRdSsgFZP8QjXf2IKcxDzFYI5H2g6ZecmR_m8oHAyrEhzs1JtyC2KnMB-Pem1jcmV91jKR6j1K5CDXa-nfkLqDJiYnqHkJEnPO7tninA="
    
    print("🔑 正在验证Token...")
    if booking.use_existing_token(latest_token):
        print("✓ Token验证成功")
    else:
        print("✗ Token验证失败，请检查网络或更新token")
        return
    
    print("\n🔧 正在测试系统功能...")
    print("-" * 30)
    
    # 测试获取医生排班
    print("1. 正在获取医生排班...")
    schedules = booking.get_doctor_schedule()
    if schedules:
        print(f"   ✓ 成功！找到 {len(schedules)} 个医院的排班信息")
    else:
        print("   ✗ 失败！无法获取医生排班")
        return
    
    # 测试获取就诊人列表
    print("2. 正在获取就诊人列表...")
    patients = booking.get_patient_list()
    if patients:
        print(f"   ✓ 成功！找到 {len(patients)} 个就诊人")
    else:
        print("   ✗ 失败！无法获取就诊人列表")
        return
    
    # 测试查找可用时间段
    print("3. 正在查找可用时间段...")
    slot_info = booking.find_available_slot()
    if slot_info:
        _, _, time_slot = slot_info
        print(f"   ✓ 成功！找到可用时间段: {time_slot}")
    else:
        print("   ✗ 当前没有可用时间段")
    
    print("\n" + "=" * 50)
    print("系统测试完成！所有功能正常！")
    print("=" * 50)
    
    # 询问用户要执行的操作
    print("\n请选择要执行的操作：")
    print("1 - 立即开始抢号")
    print("2 - 设置定时抢号")
    print("3 - 仅测试，不抢号")
    
    try:
        choice = input("\n请输入选择 (1/2/3): ").strip()
        
        if choice == "1":
            print("\n🚀 开始立即抢号...")
            print("=" * 30)
            success = booking.run_booking_task()
            if success:
                print("\n🎉 抢号成功！")
            else:
                print("\n😞 抢号失败，请稍后重试")
                
        elif choice == "2":
            booking_time = booking.config['booking_config']['booking_time']
            print(f"\n⏰ 设置定时抢号，将在 {booking_time} 开始")
            print("程序将保持运行，等待执行时间...")
            print("按 Ctrl+C 可以停止")
            try:
                booking.schedule_booking()
            except KeyboardInterrupt:
                print("\n定时任务已停止")
                
        elif choice == "3":
            print("\n✅ 测试完成，程序结束")
            
        else:
            print("\n❌ 无效的选择")
            
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
    
    print("\n程序结束，感谢使用！")

if __name__ == "__main__":
    main()
    # 在VSCode中运行时，防止窗口立即关闭
    if 'VSCODE_PID' in os.environ or 'TERM_PROGRAM' in os.environ:
        input("\n按回车键退出...")
